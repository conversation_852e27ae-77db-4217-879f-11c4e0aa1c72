#!/usr/bin/env node
import readline from 'readline';
import chalk from 'chalk';
/**
 * Main entry point for the AgentZero CLI application.
 *
 * This creates a minimal Gemini CLI-style interface that stays in the main terminal
 * without creating separate spaces or clearing the screen.
 */
// Handle uncaught exceptions gracefully
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
/**
 * Creates an output box with content
 */
function createOutputBox(content) {
    const width = Math.min(process.stdout.columns - 4, 80);
    const topBorder = '┌─ ' + chalk.bold('Output') + ' ' + '─'.repeat(width - 11) + '┐';
    const bottomBorder = '└' + '─'.repeat(width - 2) + '┘';
    const lines = content.split('\n');
    const contentLines = lines.map(line => {
        const padding = ' '.repeat(Math.max(0, width - line.length - 3));
        return '│ ' + line + padding + '│';
    });
    return chalk.green(topBorder) + '\n' +
        contentLines.join('\n') + '\n' +
        chalk.green(bottomBorder);
}
/**
 * Creates a simple input box similar to Gemini CLI
 */
function createInputBox() {
    const width = Math.min(process.stdout.columns - 4, 80);
    const topBorder = '┌─ ' + chalk.bold('Input') + ' ' + '─'.repeat(width - 10) + '┐';
    const bottomBorder = '└' + '─'.repeat(width - 2) + '┘';
    const prompt = '│ > ';
    const placeholderText = 'Type your message or @path/to/file';
    const padding = ' '.repeat(Math.max(0, width - prompt.length - placeholderText.length - 1));
    return chalk.cyan(topBorder) + '\n' +
        chalk.cyan(prompt) + chalk.dim(placeholderText) + chalk.cyan(padding + '│') + '\n' +
        chalk.cyan(bottomBorder);
}
/**
 * Main CLI loop
 */
async function main() {
    console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
    console.log(chalk.dim('Press Ctrl+C to exit\n'));
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
        prompt: ''
    });
    // Show initial input box
    console.log(createInputBox());
    rl.on('line', async (input) => {
        const trimmedInput = input.trim();
        if (!trimmedInput) {
            console.log('\n' + createInputBox());
            return;
        }
        if (trimmedInput.toLowerCase() === 'exit' || trimmedInput.toLowerCase() === 'quit') {
            console.log(chalk.dim('\n👋 Goodbye!'));
            rl.close();
            return;
        }
        if (trimmedInput.toLowerCase() === 'clear') {
            console.clear();
            console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
            console.log(chalk.dim('Press Ctrl+C to exit\n'));
            console.log(createInputBox());
            return;
        }
        // Process the input (for now, just echo)
        const response = `Echo: ${trimmedInput}`;
        // Show output in a box
        console.log('\n' + createOutputBox(response));
        // Show new input box
        console.log('\n' + createInputBox());
    });
    rl.on('SIGINT', () => {
        console.log(chalk.dim('\n👋 Goodbye!'));
        process.exit(0);
    });
}
// Start the CLI
main().catch((error) => {
    console.error('Error starting CLI:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map