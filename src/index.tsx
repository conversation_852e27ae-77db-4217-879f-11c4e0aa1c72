#!/usr/bin/env node

import { render } from 'ink';
import { App } from './components/App.js';

/**
 * Main entry point for the AgentZero CLI application.
 * 
 * This file initializes the Ink-based TUI application that mimics
 * the visual style of Gemini CLI with rectangular input/output sections.
 */

// Handle uncaught exceptions gracefully
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Render the main App component
const { clear, unmount } = render(<App />);

// Handle graceful shutdown
process.on('SIGINT', () => {
  clear();
  unmount();
  console.log('\n👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  clear();
  unmount();
  process.exit(0);
});
