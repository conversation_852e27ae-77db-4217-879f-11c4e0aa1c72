#!/usr/bin/env node

import readline from 'readline';
import chalk from 'chalk';

// Handle errors gracefully
process.on('uncaughtException', (error) => {
  console.error('Error:', error);
  process.exit(1);
});

// Create boxes
const createBox = (label: string, content: string, color: any) => {
  const w = Math.min(process.stdout.columns - 4, 80);
  const top = `┌─ ${label} ${'─'.repeat(w - label.length - 5)}┐`;
  const lines = content.split('\n').map(line => {
    const pad = ' '.repeat(Math.max(0, w - line.length - 3));
    return `│ ${line}${pad}│`;
  });
  const bottom = `└${'─'.repeat(w - 2)}┘`;
  return color(top) + '\n' + lines.join('\n') + '\n' + color(bottom);
};

const inputBox = () => createBox('Input', '> Type your message or @path/to/file', chalk.cyan);
const outputBox = (text: string) => createBox('Output', text, chalk.green);

// Main CLI
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: ''
});

console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
console.log(chalk.dim('Press Ctrl+C to exit\n'));
console.log(inputBox());

rl.on('line', (input: string) => {
  const cmd = input.trim();

  if (!cmd) {
    console.log('\n' + inputBox());
    return;
  }

  if (cmd === 'exit' || cmd === 'quit') {
    console.log(chalk.dim('\n👋 Goodbye!'));
    rl.close();
    return;
  }

  if (cmd === 'clear') {
    console.clear();
    console.log(chalk.bold.cyan('\n🤖 AgentZero CLI'));
    console.log(chalk.dim('Press Ctrl+C to exit\n'));
    console.log(inputBox());
    return;
  }

  // Process input
  console.log('\n' + outputBox(`Echo: ${cmd}`));
  console.log('\n' + inputBox());
});

rl.on('SIGINT', () => {
  console.log(chalk.dim('\n👋 Goodbye!'));
  process.exit(0);
});
