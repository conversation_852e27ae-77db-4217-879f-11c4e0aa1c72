import React, { useState, useCallback } from 'react';
import { Box } from 'ink';
import { InputBox } from './InputBox.js';
import { OutputBox } from './OutputBox.js';
import { Header } from './Header.js';

/**
 * Main application component that orchestrates the CLI interface.
 * 
 * Features:
 * - Header with application title and instructions
 * - Input box for user prompts (supports multi-line input)
 * - Output box for displaying responses
 * - Clean separation between input and output sections
 * - Gemini CLI-style rectangular layout
 */

export interface OutputMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'system' | 'error';
}

export const App: React.FC = () => {
  const [outputMessages, setOutputMessages] = useState<OutputMessage[]>([
    {
      id: '1',
      content: 'Welcome to AgentZero CLI! Type your message below and press Enter to submit.',
      timestamp: new Date(),
      type: 'system'
    }
  ]);

  const [isProcessing, setIsProcessing] = useState(false);

  /**
   * Handles user input submission from the InputBox component.
   * For now, this simply echoes the input back to the output.
   * This is where you would integrate AI services in the future.
   */
  const handleInputSubmit = useCallback((input: string) => {
    if (!input.trim()) {
      return;
    }

    setIsProcessing(true);

    // Add user message to output
    const userMessage: OutputMessage = {
      id: Date.now().toString(),
      content: input,
      timestamp: new Date(),
      type: 'user'
    };

    setOutputMessages(prev => [...prev, userMessage]);

    // Simulate processing delay (replace with actual AI integration)
    setTimeout(() => {
      const systemResponse: OutputMessage = {
        id: (Date.now() + 1).toString(),
        content: `Echo: ${input}`,
        timestamp: new Date(),
        type: 'system'
      };

      setOutputMessages(prev => [...prev, systemResponse]);
      setIsProcessing(false);
    }, 500);
  }, []);

  /**
   * Handles clearing the output messages.
   * Useful for resetting the interface.
   */
  const handleClear = useCallback(() => {
    setOutputMessages([
      {
        id: Date.now().toString(),
        content: 'Output cleared. Ready for new input.',
        timestamp: new Date(),
        type: 'system'
      }
    ]);
  }, []);

  return (
    <Box flexDirection="column" width="100%" height="100%">
      {/* Application Header */}
      <Header />
      
      {/* Main Content Area */}
      <Box flexDirection="column" flexGrow={1} paddingX={1}>
        {/* Output Section */}
        <OutputBox 
          messages={outputMessages} 
          isProcessing={isProcessing}
          onClear={handleClear}
        />
        
        {/* Spacing between sections */}
        <Box height={1} />
        
        {/* Input Section */}
        <InputBox 
          onSubmit={handleInputSubmit}
          disabled={isProcessing}
        />
      </Box>
    </Box>
  );
};
