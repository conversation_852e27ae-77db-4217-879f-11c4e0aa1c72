import React, { useMemo } from 'react';
import { Box, Text } from 'ink';
import { OutputMessage } from './App.js';

/**
 * OutputBox component that displays messages in a rectangular area similar to Gemini CLI.
 * 
 * Features:
 * - Scrollable message history
 * - Different styling for different message types (user, system, error)
 * - Timestamps for each message
 * - Loading indicator when processing
 * - Clear visual boundaries with borders
 * - Support for long content with proper wrapping
 */

interface OutputBoxProps {
  messages: OutputMessage[];
  isProcessing?: boolean;
  onClear?: () => void;
}

export const OutputBox: React.FC<OutputBoxProps> = ({ 
  messages, 
  isProcessing = false,
  onClear 
}) => {
  /**
   * Format timestamp for display
   */
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  /**
   * Get color for message type
   */
  const getMessageColor = (type: OutputMessage['type']): string => {
    switch (type) {
      case 'user':
        return 'blue';
      case 'system':
        return 'green';
      case 'error':
        return 'red';
      default:
        return 'white';
    }
  };

  /**
   * Get prefix for message type
   */
  const getMessagePrefix = (type: OutputMessage['type']): string => {
    switch (type) {
      case 'user':
        return '👤';
      case 'system':
        return '🤖';
      case 'error':
        return '❌';
      default:
        return '💬';
    }
  };

  /**
   * Render individual message with proper formatting
   */
  const renderMessage = (message: OutputMessage) => {
    const color = getMessageColor(message.type);
    const prefix = getMessagePrefix(message.type);
    const timestamp = formatTime(message.timestamp);

    return (
      <Box key={message.id} flexDirection="column" marginBottom={1}>
        {/* Message Header */}
        <Box>
          <Text color={color} bold>
            {prefix} {message.type.toUpperCase()}
          </Text>
          <Text dimColor> • {timestamp}</Text>
        </Box>
        
        {/* Message Content */}
        <Box paddingLeft={2}>
          <Text color={color}>
            {message.content}
          </Text>
        </Box>
      </Box>
    );
  };

  /**
   * Render processing indicator
   */
  const renderProcessingIndicator = () => {
    if (!isProcessing) return null;

    return (
      <Box marginTop={1}>
        <Text color="yellow">
          ⏳ Processing...
        </Text>
      </Box>
    );
  };

  /**
   * Calculate the display messages (limit to recent messages for performance)
   */
  const displayMessages = useMemo(() => {
    // Show last 50 messages to prevent performance issues
    return messages.slice(-50);
  }, [messages]);

  return (
    <Box flexDirection="column" flexGrow={1}>
      {/* Output Section Label */}
      <Box justifyContent="space-between">
        <Text bold color="cyan">
          Output:
        </Text>
        {onClear && messages.length > 1 && (
          <Text dimColor>
            {messages.length} messages • Type 'clear' to reset
          </Text>
        )}
      </Box>
      
      {/* Output Box with Border */}
      <Box 
        flexDirection="column" 
        borderStyle="round" 
        borderColor="cyan"
        paddingX={1}
        paddingY={1}
        flexGrow={1}
        minHeight={10}
      >
        {/* Messages Container */}
        <Box flexDirection="column" flexGrow={1}>
          {displayMessages.length === 0 ? (
            <Box justifyContent="center" alignItems="center" flexGrow={1}>
              <Text dimColor>
                No messages yet. Start typing below!
              </Text>
            </Box>
          ) : (
            displayMessages.map(renderMessage)
          )}
          
          {/* Processing Indicator */}
          {renderProcessingIndicator()}
        </Box>
        
        {/* Footer with scroll indicator if needed */}
        {messages.length > 50 && (
          <Box marginTop={1} justifyContent="center">
            <Text dimColor>
              Showing last 50 messages • {messages.length - 50} older messages hidden
            </Text>
          </Box>
        )}
      </Box>
    </Box>
  );
};
