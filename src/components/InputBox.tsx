import React, { useState, useCallback } from 'react';
import { Box, Text, useInput, Key } from 'ink';

/**
 * InputBox component that provides a rectangular input area similar to Gemini CLI.
 * 
 * Features:
 * - Multi-line input support
 * - Clear visual boundaries with borders
 * - Keyboard navigation (Enter to submit, Ctrl+C handled by parent)
 * - Input validation and empty input handling
 * - Visual feedback for disabled state
 */

interface InputBoxProps {
  onSubmit: (input: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export const InputBox: React.FC<InputBoxProps> = ({ 
  onSubmit, 
  disabled = false,
  placeholder = "Type your message here..."
}) => {
  const [input, setInput] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);

  /**
   * Handle keyboard input for the input box.
   * Supports basic text editing, Enter to submit, and Backspace.
   */
  useInput((inputChar: string, key: Key) => {
    if (disabled) return;

    if (key.return) {
      // Submit on Enter
      if (input.trim()) {
        onSubmit(input.trim());
        setInput('');
        setCursorPosition(0);
      } else if (input === 'clear') {
        // Special command to clear output
        onSubmit('clear');
        setInput('');
        setCursorPosition(0);
      }
      return;
    }

    if (key.backspace || key.delete) {
      // Handle backspace
      if (cursorPosition > 0) {
        const newInput = input.slice(0, cursorPosition - 1) + input.slice(cursorPosition);
        setInput(newInput);
        setCursorPosition(cursorPosition - 1);
      }
      return;
    }

    if (key.leftArrow) {
      // Move cursor left
      setCursorPosition(Math.max(0, cursorPosition - 1));
      return;
    }

    if (key.rightArrow) {
      // Move cursor right
      setCursorPosition(Math.min(input.length, cursorPosition + 1));
      return;
    }

    if (key.ctrl && inputChar === 'c') {
      // Ctrl+C is handled by the parent process
      return;
    }

    // Regular character input
    if (inputChar && !key.ctrl && !key.meta) {
      const newInput = input.slice(0, cursorPosition) + inputChar + input.slice(cursorPosition);
      setInput(newInput);
      setCursorPosition(cursorPosition + inputChar.length);
    }
  });

  /**
   * Renders the input text with cursor positioning.
   * Shows a blinking cursor at the current position.
   */
  const renderInputWithCursor = useCallback(() => {
    if (input.length === 0) {
      return (
        <Text dimColor={!disabled}>
          {disabled ? 'Processing...' : placeholder}
          {!disabled && <Text backgroundColor="white" color="black"> </Text>}
        </Text>
      );
    }

    const beforeCursor = input.slice(0, cursorPosition);
    const atCursor = input.slice(cursorPosition, cursorPosition + 1) || ' ';
    const afterCursor = input.slice(cursorPosition + 1);

    return (
      <Text>
        {beforeCursor}
        {!disabled && (
          <Text backgroundColor="white" color="black">
            {atCursor}
          </Text>
        )}
        {afterCursor}
      </Text>
    );
  }, [input, cursorPosition, disabled, placeholder]);

  return (
    <Box flexDirection="column">
      {/* Input Section Label */}
      <Box>
        <Text bold color={disabled ? "gray" : "green"}>
          Input:
        </Text>
      </Box>
      
      {/* Input Box with Border */}
      <Box 
        flexDirection="column" 
        borderStyle="round" 
        borderColor={disabled ? "gray" : "green"}
        paddingX={1}
        paddingY={1}
        minHeight={4}
      >
        {/* Input Content */}
        <Box>
          {renderInputWithCursor()}
        </Box>
        
        {/* Input Instructions */}
        <Box marginTop={1}>
          <Text dimColor>
            {disabled 
              ? 'Please wait...' 
              : 'Press Enter to submit • Use arrow keys to navigate'
            }
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
